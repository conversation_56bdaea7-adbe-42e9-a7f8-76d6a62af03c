# AugmentTool - Augment AI 插件数据清理工具配置文件
# 这个配置文件帮助 Augment AI 助手更好地理解项目结构和上下文

# 项目基本信息
project_name: "AugmentTool"
project_type: "Qt C++ Desktop Application"
description: "专门用于清理 Augment AI 编程助手插件数据的桌面应用程序"
version: "1.0.0"
language: "C++"
framework: "Qt 5.12+"
repository: "https://gitee.com/beipiao_boy/augment-tool.git"

# 项目结构说明
structure:
  core_modules:
    - "core/AugmentTelemetryManager": "遥测数据管理器，处理 Augment 插件的机器ID、设备ID等标识符"
    - "core/AugmentDatabaseManager": "数据库管理器，处理 AI 交互记录和代码缓存"
    - "core/AugmentCacheManager": "缓存管理器，清理模型数据和个性化信息"
    - "core/BackupManager": "备份管理器，提供安全备份和恢复功能"
    - "core/Logger": "日志系统，记录所有操作和错误信息"
    - "core/LicenseManager": "许可证管理器，处理软件激活和授权"
    - "core/HardwareFingerprint": "硬件指纹识别，用于设备唯一标识"
    - "core/ConfigurationManager": "配置管理器，处理应用程序设置"

  ui_modules:
    - "ui/MainWindow": "主窗口界面，提供主要功能入口"
    - "ui/SettingsDialog": "设置对话框，配置清理规则和行为"
    - "ui/ModernSplashScreen": "现代化启动画面"
    - "ui/ActivationDialog": "激活对话框，处理软件授权"

  tools:
    - "tools/ActivationCodeGenerator": "激活码生成工具"

  documentation:
    - "docs/": "详细的功能文档和设计说明"

# 主要功能模块
features:
  - "Augment 遥测清理": "修改插件的机器ID、设备ID、用户ID等遥测标识符"
  - "AI 交互记录清理": "清理代码补全历史、AI 对话记录、代码片段缓存"
  - "模型数据清理": "删除缓存的 AI 模型数据和个性化信息"
  - "使用统计重置": "清理使用统计、性能指标和行为分析数据"
  - "一键清理": "自动执行所有相关清理操作"
  - "安全备份": "操作前自动创建备份，支持一键恢复"
  - "许可证管理": "软件激活和授权验证"

# 技术栈
tech_stack:
  language: "C++11"
  gui_framework: "Qt 5.12+"
  database: "SQLite"
  build_system: "qmake"
  platform: "跨平台 (Windows, macOS, Linux)"

# 构建配置
build:
  primary_file: "AugmentTool.pro"
  target: "AugmentTool"
  config: "c++11"
  qt_modules: ["core", "widgets", "sql", "network", "concurrent", "xml"]
  resources: "resources.qrc"
  manifest: "AugmentTool.exe.manifest"
  icon: "icons/Augment.ico"

# 重要文件说明
important_files:
  - "main.cpp": "程序入口点"
  - "AugmentTool.pro": "qmake 项目配置文件"
  - "resources.qrc": "Qt 资源文件"
  - "AugmentTool_resource.rc": "Windows 资源文件"
  - "AugmentTool.exe.manifest": "Windows 应用程序清单（管理员权限）"
  - "README.md": "项目详细说明文档"

# 开发注意事项
development_notes:
  - "需要管理员权限执行某些清理操作"
  - "所有操作前自动创建备份以确保数据安全"
  - "支持多语言界面（当前为中文）"
  - "使用现代化的 UI 设计风格"
  - "包含完整的错误处理和日志记录"
  - "支持激活码验证和许可证管理"

# 安全特性
security:
  - "本地处理，不上传任何数据"
  - "严格的文件操作权限验证"
  - "防止路径遍历攻击"
  - "多次覆写确保数据安全删除"
  - "完整性验证和恢复机制"

# 部署信息
deployment:
  installer: "installer/AugmentTool_v1.0.0_Setup.exe"
  package_dir: "package/"
  required_dlls: ["Qt5Core.dll", "Qt5Gui.dll", "Qt5Widgets.dll", "Qt5Network.dll", "Qt5Sql.dll"]

# 测试和质量保证
testing:
  - "包含单元测试框架"
  - "支持代码覆盖率分析"
  - "自动化构建和测试流程"

# 文档结构
docs_structure:
  - "激活系统设计.md": "激活系统设计文档"
  - "备份修复功能文档.md": "备份修复功能文档"
  - "图标集成说明.md": "图标集成说明文档"
  - "现代化启动画面设计.md": "现代化启动画面设计"
  - "一键清理逻辑说明.md": "一键清理逻辑说明"
  - "进程退出修复.md": "进程退出修复文档"
  - "说明文档.md": "文档目录说明"
  - "刷新功能说明.md": "刷新功能说明文档"
  - "设置对话框设计.md": "设置对话框设计"
  - "简化操作流程.md": "简化操作流程"
  - "系统架构设计.md": "系统架构设计文档"
  - "Augment数据结构.md": "Augment数据结构说明"
  - "构建指南.md": "项目构建指南"
  - "核心模块说明.md": "核心模块说明文档"
  - "开发环境配置.md": "开发环境配置指南"
  - "需求规格说明.md": "需求规格说明书"
  - "安全设计文档.md": "安全设计文档"
  - "技术栈说明.md": "技术栈说明文档"
  - "测试计划.md": "测试计划文档"
  - "刷新功能测试.md": "刷新功能测试文档"
  - "界面设计说明.md": "界面设计说明文档"
  - "用户手册.md": "用户使用手册"
  - "用户故事.md": "用户故事文档"

# AI 助手使用指南
ai_assistant_guidelines:
  - "这是一个专业的桌面应用程序项目，注重用户体验和数据安全"
  - "所有代码修改都应该考虑跨平台兼容性"
  - "UI 相关的修改需要考虑现代化设计风格"
  - "任何涉及文件操作的功能都必须包含备份机制"
  - "新功能应该有相应的日志记录和错误处理"
  - "遵循 Qt 编程最佳实践和 C++ 现代标准"