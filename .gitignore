# =============================================================================
# AugmentTool - Qt C++ 桌面应用程序 .gitignore 配置
# =============================================================================

# -----------------------------------------------------------------------------
# Qt 相关文件
# -----------------------------------------------------------------------------

# Qt 构建输出
build*/
debug/
release/
Debug/
Release/
x64/
Win32/

# Qt 生成的文件
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.moc

# Qt 项目用户配置文件
*.pro.user
*.pro.user.*
*.qws

# Qt 翻译文件（编译后的）
*.qm

# Qt 设计师备份文件
*.ui.autosave

# Qt 临时文件
.qmake.cache
.qmake.stash

# -----------------------------------------------------------------------------
# C++ 编译产物
# -----------------------------------------------------------------------------

# 目标文件
*.o
*.obj
*.lo
*.slo

# 预编译头文件
*.gch
*.pch

# 编译的动态库
*.so
*.so.*
*.dylib
*.dll

# 编译的静态库
*.a
*.la
*.lib

# 可执行文件
*.exe
*.out
*.app

# 链接器输出
*.ilk
*.map
*.exp

# 调试信息
*.pdb
*.idb

# -----------------------------------------------------------------------------
# 构建系统文件
# -----------------------------------------------------------------------------

# CMake
CMakeCache.txt
CMakeFiles/
CMakeScripts/
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps/

# qmake
Makefile*
!Makefile.am
!Makefile.in

# Ninja
.ninja_deps
.ninja_log

# -----------------------------------------------------------------------------
# IDE 和编辑器文件
# -----------------------------------------------------------------------------

# Qt Creator
*.autosave
*.user
*.user.*

# Visual Studio
.vs/
*.vcxproj.user
*.vcxproj.filters
*.VC.db
*.VC.VC.opendb

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# CLion
.idea/
cmake-build-*/

# Code::Blocks
*.cbp
*.layout
*.depend

# Dev-C++
*.dev

# 通用编辑器文件
*.swp
*.swo
*~
.#*

# -----------------------------------------------------------------------------
# 操作系统文件
# -----------------------------------------------------------------------------

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.fseventsd
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# -----------------------------------------------------------------------------
# 日志和临时文件
# -----------------------------------------------------------------------------

# 应用程序日志
*.log
*.log.*
logs/
AugmentTool_Log_*.txt

# 临时文件
*.tmp
*.temp
.tmp/
temp/

# 备份文件
*.bak
*.backup
*.orig
*.rej

# -----------------------------------------------------------------------------
# 测试和调试文件
# -----------------------------------------------------------------------------

# 测试输出
test-results/
test_output/
*.test

# 调试文件
*.dmp
core
core.*

# 性能分析文件
*.prof
*.gprof
callgrind.out.*

# -----------------------------------------------------------------------------
# 打包和分发文件
# -----------------------------------------------------------------------------

# 安装包构建目录
installer/build/
package/build/

# 压缩文件
*.zip
*.tar.gz
*.tar.bz2
*.7z
*.rar

# 但保留预构建的安装包
!installer/*.exe
!package/*.exe

# -----------------------------------------------------------------------------
# 数据库和配置文件
# -----------------------------------------------------------------------------

# SQLite 数据库文件
*.sqlite
*.sqlite3
*.db
*.db-journal

# 配置文件（敏感信息）
config.ini
settings.ini
*.conf
!default.conf

# 许可证密钥文件
*.key
*.pem
license.dat

# -----------------------------------------------------------------------------
# 缓存和运行时文件
# -----------------------------------------------------------------------------

# 应用程序缓存
cache/
.cache/

# 运行时数据
*.pid
*.lock

# -----------------------------------------------------------------------------
# 版本控制和 Git 相关
# -----------------------------------------------------------------------------

# Git 合并冲突文件
*.orig

# Git 补丁文件
*.patch

# -----------------------------------------------------------------------------
# Augment AI 配置
# -----------------------------------------------------------------------------

# 重要：确保 .augment 文件夹被跟踪
# .augment/ 文件夹包含项目的 AI 助手配置，应该被版本控制
# 但排除可能的临时文件
.augment/temp/
.augment/cache/
.augment/*.tmp

# 确保 .augmentrc 配置文件被跟踪
!.augmentrc

# -----------------------------------------------------------------------------
# 项目特定排除
# -----------------------------------------------------------------------------

# 开发测试文件
test_*.cpp
test_*.h
debug_*.cpp
debug_*.h
compile_test.*
run_test.*
*_test.ps1

# 实验性代码
experimental/
sandbox/

# 文档草稿
docs/draft/
docs/*.draft.md

# 本地配置覆盖
local.pro
local.pri

sync_config.jsonc