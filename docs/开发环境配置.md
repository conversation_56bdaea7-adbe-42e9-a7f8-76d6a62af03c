# 开发环境搭建指南

## 1. 系统要求

### 1.1 硬件要求
- **CPU**: x64 架构处理器
- **内存**: 最少 8GB RAM，推荐 16GB
- **存储**: 至少 10GB 可用空间
- **网络**: 稳定的互联网连接（用于下载依赖）

### 1.2 操作系统支持
- **Windows**: Windows 10/11 (64位)
- **macOS**: macOS 10.15+ (Catalina 及以上)
- **Linux**: Ubuntu 18.04+, CentOS 8+, Fedora 32+

## 2. 基础工具安装

### 2.1 Git 版本控制

#### Windows
```bash
# 下载并安装 Git for Windows
# https://git-scm.com/download/win

# 验证安装
git --version
```

#### macOS
```bash
# 使用 Homebrew 安装
brew install git

# 或使用 Xcode Command Line Tools
xcode-select --install
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install git

# 验证安装
git --version
```

### 2.2 CMake 构建系统

#### Windows
```bash
# 下载并安装 CMake
# https://cmake.org/download/

# 或使用 Chocolatey
choco install cmake

# 验证安装
cmake --version
```

#### macOS
```bash
# 使用 Homebrew
brew install cmake

# 验证安装
cmake --version
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt install cmake

# CentOS/RHEL
sudo yum install cmake3
sudo ln -s /usr/bin/cmake3 /usr/bin/cmake

# 验证安装
cmake --version
```

## 3. C++ 开发环境

### 3.1 编译器安装

#### Windows - Visual Studio
```bash
# 下载 Visual Studio 2019/2022 Community
# https://visualstudio.microsoft.com/downloads/

# 必需组件:
# - MSVC v143 编译器工具集
# - Windows 10/11 SDK
# - CMake 工具
# - Git for Windows
```

#### macOS - Xcode
```bash
# 安装 Xcode
xcode-select --install

# 或从 App Store 安装完整 Xcode

# 验证安装
clang --version
```

#### Linux - GCC/Clang
```bash
# Ubuntu/Debian
sudo apt install build-essential gcc g++ clang

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install gcc gcc-c++ clang

# 验证安装
gcc --version
g++ --version
```

### 3.2 Qt 框架安装

#### 在线安装器 (推荐)
```bash
# 下载 Qt Online Installer
# https://www.qt.io/download-qt-installer

# 安装组件:
# - Qt 6.5.x
# - Qt Creator IDE
# - CMake
# - Ninja
```

#### 离线安装
```bash
# Windows
# 下载 Qt 6.5.x for Windows
# 选择 MSVC 2019 64-bit 组件

# macOS
# 下载 Qt 6.5.x for macOS
# 选择 macOS 组件

# Linux
# 下载 Qt 6.5.x for Linux
# 选择 GCC 64-bit 组件
```

#### 包管理器安装 (Linux)
```bash
# Ubuntu 22.04+
sudo apt install qt6-base-dev qt6-tools-dev qt6-tools-dev-tools

# Fedora
sudo dnf install qt6-qtbase-devel qt6-qttools-devel

# Arch Linux
sudo pacman -S qt6-base qt6-tools
```

## 4. Python 开发环境

### 4.1 Python 安装

#### Windows
```bash
# 下载 Python 3.9+ from python.org
# https://www.python.org/downloads/windows/

# 或使用 Microsoft Store
# 搜索 "Python 3.9" 或更高版本

# 验证安装
python --version
pip --version
```

#### macOS
```bash
# 使用 Homebrew (推荐)
brew install python@3.9

# 或下载官方安装包
# https://www.python.org/downloads/macos/

# 验证安装
python3 --version
pip3 --version
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt install python3 python3-pip python3-dev

# CentOS/RHEL
sudo yum install python3 python3-pip python3-devel

# 验证安装
python3 --version
pip3 --version
```

### 4.2 Python 依赖安装

#### 创建虚拟环境
```bash
# 创建项目虚拟环境
python -m venv vscode_cleaner_env

# 激活虚拟环境
# Windows
vscode_cleaner_env\Scripts\activate

# macOS/Linux
source vscode_cleaner_env/bin/activate

# 升级 pip
pip install --upgrade pip
```

#### 安装开发依赖
```bash
# 安装核心依赖
pip install pybind11 numpy pandas

# 安装开发工具
pip install pytest black flake8 mypy

# 安装文档工具
pip install sphinx sphinx-rtd-theme

# 保存依赖列表
pip freeze > requirements.txt
```

## 5. IDE 配置

### 5.1 Qt Creator 配置

#### 项目配置
```bash
# 1. 打开 Qt Creator
# 2. File -> Open File or Project
# 3. 选择项目根目录的 CMakeLists.txt
# 4. 配置构建套件 (Kit)

# 构建套件配置:
# - 编译器: GCC/Clang/MSVC
# - Qt 版本: Qt 6.5.x
# - CMake: 系统安装的 CMake
```

#### 代码风格设置
```bash
# Tools -> Options -> C++
# Code Style -> 选择或创建自定义风格

# 推荐设置:
# - 缩进: 4 spaces
# - 大括号风格: Allman
# - 指针和引用: 靠近类型名
```

### 5.2 Visual Studio Code 配置

#### 必需插件
```json
{
    "recommendations": [
        "ms-vscode.cpptools-extension-pack",
        "ms-python.python",
        "ms-vscode.cmake-tools",
        "ms-vscode.qt-tools",
        "ms-vscode.gitlens"
    ]
}
```

#### 工作区配置
```json
// .vscode/settings.json
{
    "cmake.configureOnOpen": true,
    "cmake.buildDirectory": "${workspaceFolder}/build",
    "python.defaultInterpreterPath": "./vscode_cleaner_env/bin/python",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools"
}
```

#### 调试配置
```json
// .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug AugmentTool",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/build/AugmentTool",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                }
            ]
        }
    ]
}
```

## 6. 项目构建

### 6.1 克隆项目
```bash
# 克隆仓库
git clone https://github.com/your-org/augment-tool.git
cd augment-tool

# 初始化子模块 (如果有)
git submodule update --init --recursive
```

### 6.2 构建配置

#### 创建构建目录
```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake .. -DCMAKE_BUILD_TYPE=Debug

# 或指定 Qt 路径 (如果需要)
cmake .. -DCMAKE_PREFIX_PATH=/path/to/Qt/6.5.0/gcc_64
```

#### Windows 特定配置
```bash
# 使用 Visual Studio 生成器
cmake .. -G "Visual Studio 16 2019" -A x64

# 或使用 Ninja (更快)
cmake .. -G Ninja -DCMAKE_BUILD_TYPE=Debug
```

### 6.3 编译项目
```bash
# 编译项目
cmake --build . --config Debug

# 或使用并行编译
cmake --build . --config Debug --parallel 4

# 安装 (可选)
cmake --install . --prefix ../install
```

### 6.4 运行测试
```bash
# 运行 C++ 测试
ctest --output-on-failure

# 运行 Python 测试
cd ..
python -m pytest tests/

# 运行所有测试
cmake --build build --target test
```

## 7. 开发工具配置

### 7.1 代码格式化

#### C++ 格式化 (clang-format)
```bash
# 安装 clang-format
# Ubuntu
sudo apt install clang-format

# macOS
brew install clang-format

# Windows (通过 LLVM)
# 下载 LLVM 安装包
```

#### .clang-format 配置
```yaml
# .clang-format
BasedOnStyle: Google
IndentWidth: 4
ColumnLimit: 120
AllowShortFunctionsOnASingleLine: Empty
BreakBeforeBraces: Allman
PointerAlignment: Left
ReferenceAlignment: Left
```

#### Python 格式化 (Black)
```bash
# 格式化 Python 代码
black src/python/

# 配置 pyproject.toml
[tool.black]
line-length = 88
target-version = ['py39']
```

### 7.2 静态分析

#### C++ 静态分析
```bash
# Clang-Tidy
clang-tidy src/*.cpp -- -I include

# Cppcheck
cppcheck --enable=all --inconclusive src/
```

#### Python 静态分析
```bash
# Flake8
flake8 src/python/

# MyPy 类型检查
mypy src/python/

# Pylint
pylint src/python/
```

## 8. 故障排除

### 8.1 常见问题

#### Qt 找不到
```bash
# 设置 Qt 环境变量
export Qt6_DIR=/path/to/Qt/6.5.0/gcc_64/lib/cmake/Qt6

# 或在 CMake 中指定
cmake .. -DCMAKE_PREFIX_PATH=/path/to/Qt/6.5.0/gcc_64
```

#### Python 绑定问题
```bash
# 确保 pybind11 已安装
pip install pybind11

# 检查 Python 开发头文件
# Ubuntu
sudo apt install python3-dev

# CentOS
sudo yum install python3-devel
```

#### 编译错误
```bash
# 清理构建目录
rm -rf build/*
cmake .. && make

# 检查编译器版本
gcc --version  # 需要 GCC 9+
clang --version  # 需要 Clang 10+
```

### 8.2 性能优化

#### 编译优化
```bash
# Release 构建
cmake .. -DCMAKE_BUILD_TYPE=Release

# 启用 LTO
cmake .. -DCMAKE_INTERPROCEDURAL_OPTIMIZATION=ON

# 使用 Ninja 构建器 (更快)
cmake .. -G Ninja
```

#### 并行编译
```bash
# 使用所有 CPU 核心
cmake --build . --parallel $(nproc)

# Windows
cmake --build . --parallel %NUMBER_OF_PROCESSORS%
```

## 9. 开发流程

### 9.1 日常开发
```bash
# 1. 更新代码
git pull origin main

# 2. 创建功能分支
git checkout -b feature/new-feature

# 3. 开发和测试
# ... 编写代码 ...

# 4. 运行测试
cmake --build build --target test

# 5. 提交代码
git add .
git commit -m "feat: 添加新功能"

# 6. 推送分支
git push origin feature/new-feature
```

### 9.2 代码审查
```bash
# 创建 Pull Request
# 1. 推送分支到远程仓库
# 2. 在 GitHub/GitLab 创建 PR
# 3. 等待代码审查
# 4. 根据反馈修改代码
# 5. 合并到主分支
```

这个开发环境搭建指南为开发团队提供了完整的环境配置步骤，确保所有开发者都能快速搭建一致的开发环境。
