# 测试工作流程

## 测试策略

### 测试金字塔

```
    /\
   /  \     E2E Tests (少量)
  /____\    
 /      \   Integration Tests (适量)
/__________\ Unit Tests (大量)
```

#### 测试类型分布
- **单元测试**: 70% - 测试单个函数或组件
- **集成测试**: 20% - 测试模块间交互
- **端到端测试**: 10% - 测试完整用户流程

### 测试原则

#### FIRST 原则
- **Fast**: 测试应该快速执行
- **Independent**: 测试之间应该独立
- **Repeatable**: 测试结果应该可重复
- **Self-Validating**: 测试应该有明确的通过/失败结果
- **Timely**: 测试应该及时编写

#### AAA 模式
```javascript
describe('用户服务', () => {
  it('应该创建新用户', async () => {
    // Arrange - 准备测试数据
    const userData = { name: '<PERSON>', email: '<EMAIL>' };
    
    // Act - 执行被测试的操作
    const user = await userService.createUser(userData);
    
    // Assert - 验证结果
    expect(user.id).toBeDefined();
    expect(user.name).toBe('<PERSON>');
  });
});
```

## 单元测试

### 测试框架配置

#### Jest 配置
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  collectCoverageFrom: [
    'src/**/*.{js,ts}',
    '!src/**/*.d.ts',
    '!src/**/*.test.{js,ts}',
    '!src/index.js'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js']
};
```

#### 测试环境设置
```javascript
// src/setupTests.js
import '@testing-library/jest-dom';

// 全局模拟
global.fetch = jest.fn();

// 测试前清理
beforeEach(() => {
  jest.clearAllMocks();
});
```

### 测试编写规范

#### 测试文件组织
```
src/
├── components/
│   ├── Button/
│   │   ├── Button.tsx
│   │   ├── Button.test.tsx
│   │   └── Button.stories.tsx
│   └── UserCard/
│       ├── UserCard.tsx
│       └── UserCard.test.tsx
└── services/
    ├── userService.js
    └── userService.test.js
```

#### 测试命名规范
```javascript
describe('UserService', () => {
  describe('createUser', () => {
    it('应该创建用户当提供有效数据时', () => {});
    it('应该抛出错误当邮箱已存在时', () => {});
    it('应该抛出错误当数据无效时', () => {});
  });
  
  describe('getUserById', () => {
    it('应该返回用户当ID存在时', () => {});
    it('应该返回null当ID不存在时', () => {});
  });
});
```

### 模拟和存根

#### 函数模拟
```javascript
// 模拟外部依赖
jest.mock('../services/emailService', () => ({
  sendEmail: jest.fn().mockResolvedValue(true)
}));

// 模拟模块的部分功能
jest.mock('../utils/logger', () => ({
  ...jest.requireActual('../utils/logger'),
  error: jest.fn()
}));
```

#### API 模拟
```javascript
// 模拟 fetch 请求
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({ id: 1, name: 'John' })
  })
);

// 使用 MSW (Mock Service Worker)
import { rest } from 'msw';
import { setupServer } from 'msw/node';

const server = setupServer(
  rest.get('/api/users', (req, res, ctx) => {
    return res(ctx.json([{ id: 1, name: 'John' }]));
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

### React 组件测试

#### 使用 Testing Library
```javascript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { UserCard } from './UserCard';

describe('UserCard', () => {
  const mockUser = {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>'
  };

  it('应该显示用户信息', () => {
    render(<UserCard user={mockUser} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('应该调用删除回调当点击删除按钮时', async () => {
    const onDelete = jest.fn();
    render(<UserCard user={mockUser} onDelete={onDelete} />);
    
    const deleteButton = screen.getByRole('button', { name: /删除/i });
    await userEvent.click(deleteButton);
    
    expect(onDelete).toHaveBeenCalledWith(mockUser.id);
  });
});
```

## 集成测试

### API 集成测试

#### 使用 Supertest
```javascript
import request from 'supertest';
import app from '../app';
import { setupTestDB, cleanupTestDB } from '../utils/testDB';

describe('User API', () => {
  beforeAll(async () => {
    await setupTestDB();
  });

  afterAll(async () => {
    await cleanupTestDB();
  });

  describe('POST /api/users', () => {
    it('应该创建新用户', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>'
      };

      const response = await request(app)
        .post('/api/users')
        .send(userData)
        .expect(201);

      expect(response.body.name).toBe(userData.name);
      expect(response.body.email).toBe(userData.email);
      expect(response.body.id).toBeDefined();
    });

    it('应该返回400当数据无效时', async () => {
      const invalidData = { name: '' };

      await request(app)
        .post('/api/users')
        .send(invalidData)
        .expect(400);
    });
  });
});
```

### 数据库集成测试

#### 测试数据库设置
```javascript
// utils/testDB.js
import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';

let mongoServer;

export async function setupTestDB() {
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  
  await mongoose.connect(mongoUri);
}

export async function cleanupTestDB() {
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  await mongoServer.stop();
}

export async function clearTestDB() {
  const collections = mongoose.connection.collections;
  
  for (const key in collections) {
    await collections[key].deleteMany({});
  }
}
```

## 端到端测试

### Playwright 配置

#### 基础配置
```javascript
// playwright.config.js
module.exports = {
  testDir: './e2e',
  timeout: 30000,
  use: {
    baseURL: 'http://localhost:3000',
    headless: true,
    screenshot: 'only-on-failure',
    video: 'retain-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    }
  ]
};
```

#### E2E 测试示例
```javascript
// e2e/user-management.spec.js
import { test, expect } from '@playwright/test';

test.describe('用户管理', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.click('[data-testid="login-button"]');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password');
    await page.click('[data-testid="submit"]');
  });

  test('应该能够创建新用户', async ({ page }) => {
    await page.click('[data-testid="add-user-button"]');
    await page.fill('[data-testid="user-name"]', 'John Doe');
    await page.fill('[data-testid="user-email"]', '<EMAIL>');
    await page.click('[data-testid="save-button"]');

    await expect(page.locator('[data-testid="user-list"]')).toContainText('John Doe');
  });

  test('应该能够删除用户', async ({ page }) => {
    await page.click('[data-testid="user-delete-1"]');
    await page.click('[data-testid="confirm-delete"]');

    await expect(page.locator('[data-testid="user-list"]')).not.toContainText('要删除的用户');
  });
});
```

## 测试数据管理

### 测试数据工厂

#### 使用 Factory Bot 模式
```javascript
// factories/userFactory.js
import { faker } from '@faker-js/faker';

export const userFactory = {
  build: (overrides = {}) => ({
    id: faker.datatype.uuid(),
    name: faker.name.fullName(),
    email: faker.internet.email(),
    createdAt: faker.date.recent(),
    ...overrides
  }),

  buildList: (count, overrides = {}) => {
    return Array.from({ length: count }, () => userFactory.build(overrides));
  }
};

// 使用示例
const user = userFactory.build({ name: 'John Doe' });
const users = userFactory.buildList(5);
```

### 测试数据清理

#### 自动清理策略
```javascript
// 每个测试后清理
afterEach(async () => {
  await clearTestDB();
});

// 使用事务回滚
beforeEach(async () => {
  await db.beginTransaction();
});

afterEach(async () => {
  await db.rollback();
});
```

## 测试覆盖率

### 覆盖率配置

#### 覆盖率阈值
```javascript
// jest.config.js
module.exports = {
  collectCoverageFrom: [
    'src/**/*.{js,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,ts,tsx}',
    '!src/index.tsx'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './src/services/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  }
};
```

### 覆盖率报告

#### 生成和查看报告
```bash
# 生成覆盖率报告
npm run test:coverage

# 查看 HTML 报告
open coverage/lcov-report/index.html

# CI 中上传覆盖率
npm install --save-dev codecov
npx codecov
```

## 持续集成中的测试

### GitHub Actions 配置

```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16, 18, 20]
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
      
      - name: Run unit tests
        run: npm run test:coverage
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## 测试最佳实践

### 编写可维护的测试
1. **测试应该独立**: 每个测试都应该能够独立运行
2. **测试应该快速**: 避免不必要的延迟和等待
3. **测试应该可读**: 使用描述性的测试名称和清晰的断言
4. **测试应该可靠**: 避免随机失败的测试
5. **测试应该有价值**: 测试重要的业务逻辑而不是实现细节

### 测试驱动开发 (TDD)
1. **红色**: 编写失败的测试
2. **绿色**: 编写最少的代码使测试通过
3. **重构**: 改进代码质量，保持测试通过

### 测试维护
- 定期审查和更新测试
- 删除过时或无价值的测试
- 重构重复的测试代码
- 保持测试与代码同步更新
