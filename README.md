# AugmentTool - Augment 插件数据清理工具

<div align="center">

![AugmentTool Logo](icons/Augment.ico)

[![版本](https://img.shields.io/badge/版本-v1.0.0-blue.svg)](https://gitee.com/beipiao_boy/augment-tool/releases)
[![许可证](https://img.shields.io/badge/许可证-MIT-green.svg)](LICENSE)
[![Qt](https://img.shields.io/badge/Qt-5.12+-orange.svg)](https://www.qt.io/)
[![平台](https://img.shields.io/badge/平台-Windows%20|%20macOS%20|%20Linux-lightgrey.svg)](https://gitee.com/beipiao_boy/augment-tool)
[![构建状态](https://img.shields.io/badge/构建-通过-success.svg)](https://gitee.com/beipiao_boy/augment-tool)

**一个专门用于清理 Augment AI 编程助手插件数据的桌面应用程序**

[功能特点](#-主要功能) • 
[快速开始](#-快速开始) • 
[使用指南](#-使用指南) • 
[下载安装](#-下载与安装) • 
[常见问题](#-常见问题) • 
[技术支持](#-支持与帮助)

</div>

## 🎯 项目概述

AugmentTool 是一个基于 C++/Qt 开发的专业桌面应用程序，专门用于安全地清理 Augment AI 编程助手插件的数据，包括遥测信息、AI 交互记录、代码缓存等，帮助用户保护编程隐私和释放存储空间。软件采用现代化界面设计，操作简单直观，同时提供完善的备份和恢复机制，确保数据安全。

### 为什么需要 AugmentTool？

- **保护隐私**：清除可能包含敏感代码的 AI 交互记录
- **释放空间**：删除不必要的缓存数据和模型文件
- **重置标识**：修改遥测 ID，避免跨会话用户行为分析
- **提升性能**：清理冗余数据可能提升 VSCode 和 Augment 插件性能

## ✨ 主要功能

- **🔒 Augment 遥测清理**：修改 Augment 插件的机器ID、设备ID、用户ID等遥测标识符
- **🤖 AI 交互记录清理**：清理代码补全历史、AI 对话记录、代码片段缓存
- **💾 模型数据清理**：删除 Augment 缓存的 AI 模型数据和个性化信息
- **📊 使用统计重置**：清理使用统计、性能指标和行为分析数据
- **⚡ 一键清理**：自动执行所有 Augment 相关清理操作
- **🛡️ 安全备份**：所有操作前自动创建备份，支持一键恢复
- **🔑 许可证管理**：软件激活和授权验证系统
- **🎨 现代化界面**：美观的用户界面和启动画面
- **📝 详细日志**：完整的操作记录和错误追踪

## 📸 功能截图

<table>
  <tr>
    <td><img src="docs/screenshots/main_window.png" alt="主界面" width="400"/></td>
    <td><img src="docs/screenshots/settings_dialog.png" alt="设置界面" width="400"/></td>
  </tr>
  <tr>
    <td><img src="docs/screenshots/backup_manager.png" alt="备份管理" width="400"/></td>
    <td><img src="docs/screenshots/activation_dialog.png" alt="激活界面" width="400"/></td>
  </tr>
</table>

## 🏗️ 技术架构

### 核心技术栈
- **C++11**：核心功能和性能关键模块
- **Qt 5.12+**：跨平台图形用户界面框架
- **SQLite**：数据库操作和管理
- **qmake**：Qt 官方构建系统

### 项目结构

```text
AugmentTool/
├── core/                         # 核心模块
│   ├── AugmentTelemetryManager.h/cpp    # 遥测管理器
│   ├── AugmentDatabaseManager.h/cpp     # 数据库管理器
│   ├── AugmentCacheManager.h/cpp        # 缓存管理器
│   ├── BackupManager.h/cpp              # 备份管理器
│   ├── Logger.h/cpp                     # 日志系统
│   ├── LicenseManager.h/cpp             # 许可证管理器
│   ├── HardwareFingerprint.h/cpp        # 硬件指纹识别
│   └── ConfigurationManager.h/cpp       # 配置管理器
├── ui/                           # 用户界面
│   ├── MainWindow.h/cpp          # 主窗口
│   ├── SettingsDialog.h/cpp      # 设置对话框
│   ├── ModernSplashScreen.h/cpp  # 现代化启动画面
│   ├── ActivationDialog.h/cpp    # 激活对话框
│   └── mainwindow.ui             # UI 设计文件
├── docs/                         # 中文文档
│   ├── screenshots/              # 截图目录
│   ├── 激活系统设计.md            # 激活系统设计文档
│   ├── 备份修复功能文档.md        # 备份修复功能文档
│   ├── 现代化启动画面设计.md      # 启动画面设计
│   ├── 一键清理逻辑说明.md        # 清理逻辑说明
│   ├── 设置对话框设计.md          # 设置界面设计
│   ├── 系统架构设计.md            # 系统架构文档
│   ├── 用户手册.md                # 用户使用手册
│   └── ...                       # 其他文档
├── tools/                        # 工具集
│   ├── ActivationCodeGenerator.cpp      # 激活码生成器
│   └── ActivationCodeGeneratorWindow.h/cpp  # 生成器窗口
├── icons/                        # 图标资源
│   └── Augment.ico               # 应用程序图标
├── package/                      # 打包文件
│   ├── AugmentTool.exe           # 可执行文件
│   ├── Qt5*.dll                  # Qt 运行库
│   └── ...                       # 其他依赖文件
├── installer/                    # 安装程序
│   └── AugmentTool_v1.0.0_Setup.exe    # 安装包
├── main.cpp                      # 程序入口
├── AugmentTool.pro              # qmake 项目文件
├── resources.qrc                # Qt 资源文件
├── AugmentTool_resource.rc      # Windows 资源文件
├── AugmentTool.exe.manifest     # Windows 清单文件
├── .augmentrc                   # Augment AI 配置文件
└── README.md                    # 项目说明
```

## 📥 下载与安装

### 预编译安装包

我们为不同平台提供了预编译的安装包，您可以直接下载安装使用：

- **Windows**: [AugmentTool_v1.0.0_Setup.exe](https://gitee.com/beipiao_boy/augment-tool/releases/download/v1.0.0/AugmentTool_v1.0.0_Setup.exe)
- **macOS**: [AugmentTool_v1.0.0.dmg](https://gitee.com/beipiao_boy/augment-tool/releases/download/v1.0.0/AugmentTool_v1.0.0.dmg)
- **Linux**: [AugmentTool_v1.0.0.AppImage](https://gitee.com/beipiao_boy/augment-tool/releases/download/v1.0.0/AugmentTool_v1.0.0.AppImage)
- **便携版**: [AugmentTool_v1.0.0_Portable.zip](https://gitee.com/beipiao_boy/augment-tool/releases/download/v1.0.0/AugmentTool_v1.0.0_Portable.zip)

### 安装步骤

#### Windows
1. 下载 `AugmentTool_v1.0.0_Setup.exe`
2. 双击安装包运行
3. 按照安装向导完成安装
4. 从开始菜单或桌面快捷方式启动

#### macOS
1. 下载 `AugmentTool_v1.0.0.dmg`
2. 打开 DMG 文件
3. 将 AugmentTool 拖到应用程序文件夹
4. 从启动器或应用程序文件夹启动

#### Linux
1. 下载 `AugmentTool_v1.0.0.AppImage`
2. 添加执行权限：`chmod +x AugmentTool_v1.0.0.AppImage`
3. 直接运行：`./AugmentTool_v1.0.0.AppImage`

#### 便携版
1. 下载 `AugmentTool_v1.0.0_Portable.zip`
2. 解压到任意位置
3. 运行解压目录中的 `AugmentTool.exe`（Windows）或 `AugmentTool`（macOS/Linux）

## 🚀 快速开始

### 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **内存**: 最少 4GB RAM
- **磁盘空间**: 100MB 可用空间
- **其他**: 需要管理员权限进行某些操作

### 构建依赖

- **qmake**: Qt 5.12+ 自带
- **Qt**: 5.12+
- **C++ 编译器**: GCC 7+/Clang 6+/MSVC 2017+
- **Python**: 3.6+ (可选，用于构建脚本)

### 构建步骤

#### 1. 获取源代码

```bash
git clone https://gitee.com/beipiao_boy/augment-tool.git
cd augment-tool
```

#### 2. 安装依赖

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install -y build-essential qt5-qmake qt5-default qtbase5-dev qttools5-dev
sudo apt install -y libqt5sql5-sqlite libqt5network5 libqt5concurrent5
```

**macOS:**
```bash
brew install qt@5
export PATH="/usr/local/opt/qt@5/bin:$PATH"
```

**Windows:**
- 安装 Qt 5.12+ (推荐使用 Qt Online Installer)
- 安装 MinGW 或 Visual Studio 2017+
- 确保 qmake 在 PATH 中

#### 3. 配置环境变量

**Windows (MinGW):**
```cmd
set QTDIR=C:\Qt\5.12.12\mingw73_64
set PATH=%PATH%;%QTDIR%\bin;C:\Qt\Tools\mingw730_64\bin
```

**Windows (MSVC):**
```cmd
set QTDIR=C:\Qt\5.12.12\msvc2017_64
set PATH=%PATH%;%QTDIR%\bin
```

**macOS/Linux:**
```bash
export QTDIR=/usr/local/Qt-5.12.0
export PATH=$PATH:$QTDIR/bin
```

#### 4. 构建项目

**直接使用 qmake (推荐):**
```bash
# 生成 Makefile
qmake AugmentTool.pro CONFIG+=release

# 编译项目
make -j4  # Linux/macOS
# 或 mingw32-make (Windows MinGW)
# 或 nmake (Windows MSVC)
```

**使用 Qt Creator:**
1. 打开 Qt Creator
2. 打开项目文件 `AugmentTool.pro`
3. 配置构建套件
4. 点击构建按钮

#### 5. 运行程序

**直接运行:**
```bash
./AugmentTool      # Linux/macOS
AugmentTool.exe    # Windows
```

**或使用 Qt Creator 直接运行**

## 📖 使用指南

### 基本使用

1. **启动应用程序**
   - 确保 VSCode 已关闭
   - 以管理员身份启动 AugmentTool（推荐）

2. **检查环境**
   - 软件会自动检测 VSCode 和 Augment 插件
   - 查看状态栏确认检测结果
   - 如果状态显示为"未检测到"，点击"刷新"按钮重新检测

3. **选择操作**
   - **修改遥测ID**: 单独修改 Augment 遥测标识符
   - **清理数据库**: 清理 AI 交互记录
   - **清理缓存**: 清理模型数据和缓存
   - **一键清理**: 执行所有清理操作

4. **确认操作**
   - 仔细阅读操作说明
   - 确认后执行操作
   - 查看操作日志
   - 操作完成后会显示详细结果

### 高级功能

- **备份管理**: 
  - 点击"工具"→"备份管理"
  - 查看已创建的备份
  - 选择备份进行恢复
  - 删除过期备份

- **日志导出**: 
  - 点击"导出日志"按钮
  - 选择保存位置
  - 日志将保存为文本文件

- **设置配置**: 
  - 点击"文件"→"设置"
  - 自定义清理规则
  - 配置备份选项
  - 设置界面主题

- **激活管理**:
  - 点击"帮助"→"激活产品"
  - 输入激活码
  - 查看激活状态

## 🛡️ 安全特性

### 数据保护
- **自动备份**: 所有操作前自动创建备份
- **完整性验证**: 文件完整性检查和恢复
- **安全删除**: 多次覆写确保数据安全删除

### 隐私保护
- **本地处理**: 所有操作在本地执行，不上传数据
- **权限控制**: 严格的文件操作权限验证
- **路径验证**: 防止路径遍历攻击

### 操作安全
- **确认机制**: 重要操作需要用户确认
- **进度监控**: 实时显示操作进度
- **错误处理**: 完善的错误处理和恢复机制

## ❓ 常见问题

### 1. 使用 AugmentTool 会影响 Augment 插件的正常使用吗？
不会。AugmentTool 只清理不必要的缓存和遥测数据，不会影响 Augment 插件的核心功能。清理后，Augment 会在下次使用时自动重新生成必要的配置文件。

### 2. 清理后需要重新激活 Augment 插件吗？
不需要。AugmentTool 不会修改 Augment 的授权信息，清理后不影响插件的激活状态。

### 3. 为什么需要管理员权限？
某些 Augment 插件文件可能位于系统保护目录，需要管理员权限才能访问和修改这些文件。

### 4. 如何恢复已清理的数据？
每次操作前，AugmentTool 会自动创建备份。您可以通过"工具"→"备份管理"来恢复之前的数据。

### 5. 软件支持哪些版本的 VSCode 和 Augment？
AugmentTool 支持所有主流版本的 VSCode 和 Augment 插件。如遇到兼容性问题，请通过 Gitee Issues 或邮件联系我们。

### 6. 一键清理具体会执行哪些操作？
一键清理会依次执行：修改遥测ID、清理数据库记录、清理缓存文件三个操作，相当于手动依次点击这三个按钮。

## 🧪 测试

### 功能测试

项目包含完整的功能测试，可以通过以下方式验证：

1. **手动测试**: 运行应用程序，测试各项功能
2. **单元测试**: 针对核心模块的单元测试
3. **集成测试**: 完整流程的集成测试

### 测试文档

详细的测试说明请参考：
- [刷新功能测试.md](docs/刷新功能测试.md)
- [测试计划.md](docs/测试计划.md)

## 📦 打包和分发

### 当前可用包

项目已提供预编译的安装包：
- **Windows 安装程序**: `installer/AugmentTool_v1.0.0_Setup.exe`
- **便携版**: `package/` 目录包含所有必需文件

### 创建自定义包

**Windows (使用 Inno Setup):**
```bash
# 编译项目
qmake AugmentTool.pro CONFIG+=release
make

# 使用 Inno Setup 编译器
iscc AugmentTool.iss
```

**手动打包:**
1. 编译项目
2. 复制可执行文件到 package 目录
3. 使用 windeployqt 部署 Qt 依赖
4. 添加必要的 DLL 文件

### 支持的包格式

- **Windows**: Inno Setup 安装程序 (.exe)
- **macOS**: DMG 镜像 (.dmg)
- **Linux**: AppImage (.AppImage)
- **便携版**: 免安装压缩包 (.zip)
- **源码包**: 完整源代码压缩包 (.tar.gz)

## 🤝 贡献指南

### 开发环境设置
1. Fork 项目仓库
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 创建 Pull Request

### 代码规范
- 遵循 C++ Core Guidelines
- 使用 clang-format 格式化代码
- 编写单元测试覆盖新功能
- 更新相关文档

### 问题报告
- 使用 Gitee Issues 报告 bug
- 提供详细的重现步骤
- 包含系统信息和日志文件

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🙏 致谢

- Qt 框架提供跨平台 GUI 支持
- SQLite 提供轻量级数据库解决方案
- 所有贡献者和测试用户

## 📞 支持与帮助

### 文档资源

- **完整文档**: [docs/](docs/) 目录包含所有中文文档
- **用户手册**: [用户手册.md](docs/用户手册.md)
- **系统架构**: [系统架构设计.md](docs/系统架构设计.md)
- **开发指南**: [开发环境配置.md](docs/开发环境配置.md)

### 问题反馈

- **Gitee Issues**: [提交问题](https://gitee.com/beipiao_boy/augment-tool/issues)
- **功能建议**: [Gitee 讨论区](https://gitee.com/beipiao_boy/augment-tool)
- **邮箱联系**: <<EMAIL>>

### 激活码获取

如需激活码，请通过以下方式联系：
- 使用 `tools/` 目录下的激活码生成工具
- 联系开发者获取授权

## 🔄 更新日志

### v1.0.0 (2024-05-01)
- 首次发布
- 支持 Augment 遥测清理
- 支持 AI 交互记录清理
- 支持模型数据清理
- 支持使用统计重置
- 添加一键清理功能
- 实现安全备份和恢复
- 添加许可证管理系统

### v0.9.0 (2024-04-15)
- 内部测试版本
- 完成核心功能开发
- 修复已知问题

## 🔮 未来计划

- [ ] 添加批量处理功能
- [ ] 支持更多 AI 编程助手插件
- [ ] 增加自动更新功能
- [ ] 提供命令行接口
- [ ] 增加多语言支持

---

## ⚠️ 免责声明

**重要提示**: 使用本软件前请仔细阅读以下条款：

1. **数据安全**: 虽然软件提供自动备份功能，但强烈建议用户在使用前手动备份重要数据
2. **使用风险**: 本软件涉及系统文件操作，请确保在了解功能的前提下使用
3. **责任限制**: 开发者不对因使用本软件造成的任何数据丢失或系统问题承担责任
4. **合法使用**: 请确保在合法合规的前提下使用本软件
5. **版权声明**: 本软件仅供学习和研究使用，请勿用于商业用途

**建议**: 首次使用前，请在测试环境中验证软件功能。
