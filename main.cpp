#include<QString>
#include<QApplication>
#include "pch.h"

#include "ui/MainWindow.h"
#include "ui/ModernSplashScreen.h"
#include "ui/ActivationDialog.h"
#include "core/Logger.h"
#include "core/LicenseManager.h"

#ifdef Q_OS_WIN
#include <windows.h>
#endif

// 全局日志器
Logger *g_logger = nullptr;

// 消息处理函数
void messageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    if (g_logger) {
        Logger::LogLevel level = Logger::LogLevel::Info; // 默认值
        switch (type) {
            case QtDebugMsg:    level = Logger::LogLevel::Debug; break;
            case QtInfoMsg:     level = Logger::LogLevel::Info; break;
            case QtWarningMsg:  level = Logger::LogLevel::Warning; break;
            case QtCriticalMsg: level = Logger::LogLevel::Error; break;
            case QtFatalMsg:    level = Logger::LogLevel::Critical; break;
            default:            level = Logger::LogLevel::Info; break;
        }

        g_logger->log(level, msg, "Qt", context.function, context.line);
    }
}

// 设置应用程序样式
void setupApplicationStyle(QApplication &app)
{
    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));

    // 设置深色主题
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);

    // 应用调色板
    app.setPalette(darkPalette);

    // 设置样式表
    QString styleSheet = R"(
        QMainWindow {
            background-color: #353535;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }

        QPushButton {
            border: 1px solid #555555;
            border-radius: 3px;
            padding: 5px;
            min-width: 80px;
        }

        QPushButton:hover {
            border: 1px solid #777777;
            background-color: #404040;
        }

        QPushButton:pressed {
            background-color: #2a2a2a;
        }

        QTextEdit {
            border: 1px solid #555555;
            border-radius: 3px;
            background-color: #2a2a2a;
        }

        QProgressBar {
            border: 1px solid #555555;
            border-radius: 3px;
            text-align: center;
        }

        QProgressBar::chunk {
            background-color: #4CAF50;
            border-radius: 2px;
        }

        QCheckBox::indicator {
            width: 13px;
            height: 13px;
        }

        QCheckBox::indicator:unchecked {
            border: 1px solid #555555;
            background-color: #2a2a2a;
        }

        QCheckBox::indicator:checked {
            border: 1px solid #4CAF50;
            background-color: #4CAF50;
        }
    )";

    app.setStyleSheet(styleSheet);
}

// 设置国际化
void setupInternationalization(QApplication &app)
{
    QTranslator *qtTranslator = new QTranslator(&app);
    // Qt 5.12 使用 QLibraryInfo::TranslationsPath
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
    if (qtTranslator->load("qt_" + QLocale::system().name(),
                          QLibraryInfo::path(QLibraryInfo::TranslationsPath))) {
#else
    if (qtTranslator->load("qt_" + QLocale::system().name(),
                          QLibraryInfo::location(QLibraryInfo::TranslationsPath))) {
#endif
        app.installTranslator(qtTranslator);
    }

    QTranslator *appTranslator = new QTranslator(&app);
    if (appTranslator->load("augmenttool_" + QLocale::system().name(),
                           ":/translations")) {
        app.installTranslator(appTranslator);
    }
}

// 检查单实例
bool checkSingleInstance()
{
    QString lockFilePath = QStandardPaths::writableLocation(QStandardPaths::TempLocation)
                          + "/" + APP_NAME + ".lock";

    QFile lockFile(lockFilePath);
    if (lockFile.exists()) {
        // 读取锁文件中的PID
        if (lockFile.open(QIODevice::ReadOnly)) {
            QByteArray pidData = lockFile.readAll();
            lockFile.close();

            bool ok;
            qint64 pid = pidData.toLongLong(&ok);

            if (ok && pid > 0) {
                // 检查进程是否真的在运行
#ifdef Q_OS_WIN
                HANDLE process = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, pid);
                if (process != NULL) {
                    CloseHandle(process);
                    QMessageBox::information(nullptr, APP_NAME,
                        "AugmentTool 已经在运行中（PID: " + QString::number(pid) + "）。");
                    return false;
                } else {
                    // 进程不存在，删除过期的锁文件
                    QFile::remove(lockFilePath);
                }
#else
                // Linux/Mac: 检查 /proc/pid 目录是否存在
                if (QDir("/proc/" + QString::number(pid)).exists()) {
                    QMessageBox::information(nullptr, APP_NAME,
                        "AugmentTool 已经在运行中（PID: " + QString::number(pid) + "）。");
                    return false;
                } else {
                    // 进程不存在，删除过期的锁文件
                    QFile::remove(lockFilePath);
                }
#endif
            } else {
                // 锁文件损坏，删除它
                QFile::remove(lockFilePath);
            }
        } else {
            // 无法读取锁文件，可能权限问题
            QMessageBox::warning(nullptr, APP_NAME,
                "检测到锁文件但无法读取。如果确认没有其他实例在运行，请手动删除：\n" + lockFilePath);
            return false;
        }
    }

    // 创建锁文件
    if (lockFile.open(QIODevice::WriteOnly)) {
        lockFile.write(QString::number(QApplication::applicationPid()).toUtf8());
        lockFile.close();
        return true;
    } else {
        QMessageBox::warning(nullptr, APP_NAME,
            "无法创建锁文件，可能存在权限问题：\n" + lockFilePath);
        return false;
    }
}

// 清理锁文件
void cleanupLockFile()
{
    QString lockFilePath = QStandardPaths::writableLocation(QStandardPaths::TempLocation)
                          + "/" + APP_NAME + ".lock";

    if (QFile::exists(lockFilePath)) {
        if (QFile::remove(lockFilePath)) {
            if (g_logger) {
                g_logger->info("锁文件已清理: " + lockFilePath);
            }
        } else {
            if (g_logger) {
                g_logger->warning("无法删除锁文件: " + lockFilePath);
            }
        }
    }
}

// 检查系统要求
bool checkSystemRequirements()
{
    // 检查操作系统版本
#ifdef Q_OS_WIN
    if (QSysInfo::windowsVersion() < QSysInfo::WV_WINDOWS10) {
        QMessageBox::critical(nullptr, "系统要求",
            "此应用程序需要 Windows 10 或更高版本。");
        return false;
    }
#endif

    // 检查系统托盘支持
    if (!QSystemTrayIcon::isSystemTrayAvailable()) {
        QMessageBox::critical(nullptr, "系统托盘",
            "系统托盘不可用。某些功能可能无法正常工作。");
    }

    // 检查磁盘空间
    QString tempPath = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
    QStorageInfo storage(tempPath);
    if (storage.bytesAvailable() < 100 * 1024 * 1024) { // 100MB
        QMessageBox::warning(nullptr, "磁盘空间",
            "可用磁盘空间不足，可能影响应用程序正常运行。");
    }

    return true;
}

// 初始化应用程序目录
void initializeApplicationDirectories()
{
    // 创建应用程序数据目录
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(appDataPath);

    // 创建日志目录
    QString logPath = appDataPath + "/logs";
    QDir().mkpath(logPath);

    // 创建备份目录
    QString backupPath = appDataPath + "/backups";
    QDir().mkpath(backupPath);

    // 创建配置目录
    QString configPath = appDataPath + "/config";
    QDir().mkpath(configPath);
}

int main(int argc, char *argv[])
{
    // 创建应用程序
    QApplication app(argc, argv);

    // 设置应用程序信息
    app.setApplicationName(APP_NAME);
    app.setApplicationVersion(APP_VERSION);
    app.setOrganizationName(APP_ORGANIZATION);
    app.setOrganizationDomain(APP_DOMAIN);
    app.setApplicationDisplayName("Augment 插件数据清理工具");

    // 设置应用程序图标
    app.setWindowIcon(QIcon(":/icons/Augment.ico"));

    // 解析命令行参数
    QCommandLineParser parser;
    parser.setApplicationDescription("Augment AI 编程助手插件数据清理工具");
    parser.addHelpOption();
    parser.addVersionOption();

    QCommandLineOption debugOption(QStringList() << "d" << "debug", "启用调试模式");
    parser.addOption(debugOption);

    QCommandLineOption noGuiOption(QStringList() << "no-gui", "命令行模式（无图形界面）");
    parser.addOption(noGuiOption);

    QCommandLineOption logLevelOption("log-level", "设置日志级别 (debug|info|warning|error)", "level", "info");
    parser.addOption(logLevelOption);

    parser.process(app);

    // 检查单实例
    if (!checkSingleInstance()) {
        return 1;
    }

    // 设置退出时清理
    QObject::connect(&app, &QApplication::aboutToQuit, []() {
        if (g_logger) {
            g_logger->info("应用程序即将退出，开始清理...");
        }
        cleanupLockFile();
    });

    // 检查系统要求
    if (!checkSystemRequirements()) {
        return 1;
    }

    // 初始化目录
    initializeApplicationDirectories();

    // 初始化日志系统
    g_logger = new Logger(&app);

    // 设置日志级别
    QString logLevelStr = parser.value(logLevelOption).toLower();
    Logger::LogLevel logLevel = Logger::LogLevel::Info;
    if (logLevelStr == "debug") logLevel = Logger::LogLevel::Debug;
    else if (logLevelStr == "warning") logLevel = Logger::LogLevel::Warning;
    else if (logLevelStr == "error") logLevel = Logger::LogLevel::Error;

    g_logger->setLogLevel(logLevel);
    g_logger->setConsoleOutput(parser.isSet(debugOption));

    // 设置日志文件
    QString logFilePath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation)
                         + "/logs/augmenttool.log";
    g_logger->setLogFile(logFilePath);

    // 安装消息处理器
    qInstallMessageHandler(messageHandler);

    g_logger->info(QString("AugmentTool %1 启动").arg(APP_VERSION));
    g_logger->info(QString("Qt 版本: %1").arg(QT_VERSION_STR));
    g_logger->info(QString("操作系统: %1").arg(QSysInfo::prettyProductName()));

    // 设置应用程序样式
    setupApplicationStyle(app);

    // 设置国际化
    setupInternationalization(app);

    // 创建许可证管理器
    LicenseManager licenseManager;

    // 检查激活状态
    bool needActivation = false;
    LicenseManager::LicenseStatus status = licenseManager.getCurrentStatus();

    if (status == LicenseManager::TrialExpired) {
        needActivation = true;
        g_logger->warning("试用期已过期，需要激活");
    } else if (status == LicenseManager::TrialPeriod) {
        int hoursRemaining = licenseManager.getTrialHoursRemaining();
        g_logger->info(QString("试用期剩余 %1 小时").arg(hoursRemaining));

        // 如果剩余时间少于2小时，提醒用户激活
        if (hoursRemaining <= 2) {
            needActivation = true;
        }
    } else if (status == LicenseManager::Activated) {
        g_logger->info("软件已激活");
    }

    // 创建现代化启动画面
    ModernSplashScreen *splash = new ModernSplashScreen();
    splash->showWithAnimation();

    app.processEvents();

    // 如果需要激活，显示激活对话框
    if (needActivation || status == LicenseManager::TrialExpired) {
        splash->hideWithAnimation();
        QTimer::singleShot(500, [splash]() {
            splash->deleteLater();
        });

        ActivationDialog activationDialog(&licenseManager);
        activationDialog.setTrialExpiredMode(status == LicenseManager::TrialExpired);

        if (activationDialog.exec() != QDialog::Accepted) {
            // 用户取消激活或退出
            if (status == LicenseManager::TrialExpired) {
                g_logger->info("用户未激活软件，程序退出");
                return 0;
            }
        }
    }

    // 创建主窗口
    MainWindow window;

    // 如果没有显示激活对话框，则显示启动画面加载过程
    if (!needActivation && status != LicenseManager::TrialExpired) {
        // 模拟加载过程
        QTimer::singleShot(500, &app, [splash]() {
            splash->setLoadingStage("正在初始化组件...");
            splash->setProgress(20);
            splash->setProgressText("加载核心模块");
        });

        QTimer::singleShot(1000, &app, [splash]() {
            splash->setLoadingStage("正在检查系统环境...");
            splash->setProgress(40);
            splash->setProgressText("验证 VSCode 环境");
        });

        QTimer::singleShot(1500, &app, [splash]() {
            splash->setLoadingStage("正在加载用户配置...");
            splash->setProgress(60);
            splash->setProgressText("读取设置文件");
        });

        QTimer::singleShot(2000, &app, [splash]() {
            splash->setLoadingStage("正在准备界面...");
            splash->setProgress(80);
            splash->setProgressText("初始化用户界面");
        });

        QTimer::singleShot(2500, &app, [splash]() {
            splash->setLoadingStage("启动完成");
            splash->setProgress(100);
            splash->setProgressText("欢迎使用 AugmentTool");
        });

        // 延迟显示主窗口并隐藏启动画面
        QTimer::singleShot(3000, &app, [&window, splash]() {
            splash->hideWithAnimation();
            window.show();

            // 延迟删除启动画面
            QTimer::singleShot(1000, [splash]() {
                splash->deleteLater();
            });
        });
    } else {
        // 如果显示了激活对话框，直接显示主窗口
        window.show();
    }

    g_logger->info("主窗口创建完成");

    // 运行应用程序
    int result = app.exec();

    g_logger->info("应用程序主循环退出");

    // 强制清理资源
    cleanupLockFile();

    // 确保所有待处理的事件都被处理
    app.processEvents();

    g_logger->info("应用程序完全退出");

    // 清理全局日志器
    delete g_logger;
    g_logger = nullptr;

    return result;
}
