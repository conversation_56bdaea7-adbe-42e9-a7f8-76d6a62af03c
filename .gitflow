# Git Flow 分支策略配置
# AugmentTool 项目分支管理规范

[gitflow "branch"]
    master = master
    develop = develop

[gitflow "prefix"]
    feature = feature/
    release = release/
    hotfix = hotfix/
    support = support/
    versiontag = v

[gitflow "path"]
    hooks = .git/hooks

# 分支说明：
# master   - 生产环境分支，包含稳定的发布版本
# develop  - 开发主分支，集成所有功能开发
# feature/ - 功能开发分支，从 develop 分出，完成后合并回 develop
# release/ - 发布准备分支，从 develop 分出，准备发布时使用
# hotfix/  - 紧急修复分支，从 master 分出，修复生产环境问题
