; AugmentTool Inno Setup 安装脚本
; 编码: UTF-8 with BOM
; 版本: 1.0.0
; 作者: AugmentCode
; 描述: Augment AI Plugin Data Cleaner Tool 安装程序

; 应用程序基本信息定义
#define MyAppName "AugmentTool"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "AugmentCode"
#define MyAppURL "https://www.augmentcode.com/"
#define MyAppExeName "AugmentTool.exe"
#define MyAppIcon "F:\Code\QT\AugmentTool\package\Augment.ico"
#define MyAppDescription "Augment AI Plugin Data Cleaner Tool"
#define MyAppCopyright "Copyright (C) 2025 AugmentCode"
; 安装路径配置（相对于脚本文件）
#define MyAppExePath "F:\Code\QT\AugmentTool\package"
; 默认安装目录
#define MyAppInstallPath "C:\Program Files\AugmentTool"
; 应用程序唯一标识符（GUID）
#define MyAppId "A8F3D2E1-B4C5-4A6B-9E8F-1D2C3B4A5E6F"

[Setup]
; 应用程序标识和基本信息
; 注意: AppId 的值用于唯一标识此应用程序
; 不要为其他安装程序使用相同的 AppId 值
AppId={{{#MyAppId}}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
AppCopyright={#MyAppCopyright}

; 安装目录配置
DefaultDirName={#MyAppInstallPath}
DefaultGroupName={#MyAppPublisher}\{#MyAppName}
; 使用之前安装版本的目录，让用户可以选择
UsePreviousAppDir=yes
; 不显示选择开始菜单文件夹页面
DisableProgramGroupPage=yes

; 权限和安全设置
; 要求管理员权限进行安装
PrivilegesRequired=admin
; 允许非管理员用户运行卸载程序
PrivilegesRequiredOverridesAllowed=dialog

; 输出配置
OutputDir=installer
OutputBaseFilename={#MyAppName}_v{#MyAppVersion}_Setup
; 使用 LZMA2 压缩算法（最高压缩率）
Compression=lzma2/ultra64
SolidCompression=yes

; 界面配置
WizardStyle=modern
; 安装程序图标
SetupIconFile="F:\Code\QT\AugmentTool\package\Augment.ico"
; 卸载程序图标
UninstallDisplayIcon={app}\Augment.ico
; 安装程序窗口大小
WizardSizePercent=100

; 版本信息
VersionInfoVersion={#MyAppVersion}
VersionInfoCompany={#MyAppPublisher}
VersionInfoDescription={#MyAppDescription}
VersionInfoCopyright={#MyAppCopyright}
VersionInfoProductName={#MyAppName}
VersionInfoProductVersion={#MyAppVersion}

[Languages]
; 支持的安装语言
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "chinesesimplified"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

[Tasks]
; 用户可选择的安装任务
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: checkablealone
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
; 要安装的文件列表
; 从 package 文件夹复制所有文件（包括主程序和依赖库）
Source: "{#MyAppExePath}\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; 注意: 不要在任何共享系统文件上使用 "Flags: ignoreversion"

[Icons]
; 创建快捷方式
; 开始菜单程序组
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\Augment.ico"
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"; IconFilename: "{app}\Augment.ico"
; 桌面快捷方式（可选）
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\Augment.ico"; Tasks: desktopicon
; 快速启动栏快捷方式（可选，仅限旧版本 Windows）
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\Augment.ico"; Tasks: quicklaunchicon

[Run]
; 安装完成后运行的程序
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
; 卸载时删除的文件和文件夹
Type: filesandordirs; Name: "{app}\logs"
Type: filesandordirs; Name: "{app}\cache"
Type: filesandordirs; Name: "{app}\temp"

[Registry]
; 注册表项（如果需要）
; 这里可以添加应用程序相关的注册表设置

[Code]
// Pascal 脚本代码
var
  globalInstallPath: String;

// 初始化安装向导
procedure InitializeWizard;
begin
  WizardForm.DirEdit.Text := globalInstallPath;
end;

// 检测程序是否正在运行
function IsAppRunning(const FileName: string): Boolean;
var
  FWMIService: Variant;
  FSWbemLocator: Variant;
  FWbemObjectSet: Variant;
begin
  Result := false;
  try
    FSWbemLocator := CreateOleObject('WBEMScripting.SWBEMLocator');
    FWMIService := FSWbemLocator.ConnectServer('', 'root\CIMV2', '', '');
    FWbemObjectSet := FWMIService.ExecQuery(Format('SELECT Name FROM Win32_Process Where Name="%s"',[FileName]));
    Result := (FWbemObjectSet.Count > 0);
  except
    Result := false;
  end;
  FWbemObjectSet := Unassigned;
  FWMIService := Unassigned;
  FSWbemLocator := Unassigned;
end;

// 获取历史安装路径
function GetInstallString(): String;
var
  InstallPath: String;
begin
  InstallPath := '{#MyAppInstallPath}';
  if RegValueExists(HKLM, 'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppId}_is1', 'Inno Setup: App Path') then
    begin
      RegQueryStringValue(HKLM, 'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppId}_is1', 'Inno Setup: App Path', InstallPath)
    end;
  result := InstallPath;
end;

// 准备安装
function InitializeSetup(): Boolean;
var
  ResultStr: String;
  ResultCode: Integer;
begin
  globalInstallPath := GetInstallString();
  result := IsAppRunning('{#MyAppExeName}');
  if result then
    begin
      MsgBox('检测到 {#MyAppName} 正在运行，请先关闭程序再安装！', mbError, MB_OK);
      result := false;
    end
  else if RegQueryStringValue(HKLM, 'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#MyAppId}_is1', 'UninstallString', ResultStr) then
    begin
      if MsgBox('是否卸载已安装的 {#MyAppName}？（保留历史数据）', mbConfirmation, MB_YESNO) = IDYES then
        begin
          ResultStr := RemoveQuotes(ResultStr);
          Exec(ResultStr, '/silent', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
        end;
      result := true;
    end
  else
    begin
      result := true;
    end;
end;

// 准备卸载
function InitializeUninstall(): Boolean;
begin
  result := IsAppRunning('{#MyAppExeName}');
  if result then
    begin
      MsgBox('检测到 {#MyAppName} 正在运行，请先关闭程序再卸载！', mbError, MB_OK);
      result := false;
    end
  else
    begin
      result := true;
    end;
end;
