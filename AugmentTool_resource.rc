#include <windows.h>

// 应用程序图标
IDI_ICON1 ICON "icons/Augment.ico"

VS_VERSION_INFO VERSIONINFO
	FILEVERSION 1,0,0,0
	PRODUCTVERSION 1,0,0,0
	FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
	FILEFLAGS VS_FF_DEBUG
#else
	FILEFLAGS 0x0L
#endif
	FILEOS VOS__WINDOWS32
	FILETYPE VFT_DLL
	FILESUBTYPE 0x0L
	BEGIN
		BLOCK "StringFileInfo"
		BEGIN
			BLOCK "040904b0"
			BEGIN
				VALUE "CompanyName", "AugmentCode\0"
				VALUE "FileDescription", "Augment AI Plugin Data Cleaner Tool\0"
				VALUE "FileVersion", "*******\0"
				VALUE "LegalCopyright", "Copyright (C) 2024 AugmentCode\0"
				VALUE "OriginalFilename", "AugmentTool.exe\0"
				VALUE "ProductName", "AugmentTool\0"
				VALUE "ProductVersion", "*******\0"
			END
		END
		BLOCK "VarFileInfo"
		BEGIN
			VALUE "Translation", 0x0409, 1200
		END
	END
/* End of Version info */

