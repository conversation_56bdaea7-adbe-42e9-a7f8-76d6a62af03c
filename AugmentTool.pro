QT += core widgets sql network concurrent xml

TARGET = AugmentTool

TEMPLATE = app

CONFIG += c++11

# Qt 版本要求
lessThan(QT_MAJOR_VERSION, 5): error("This project requires Qt 5.12 or later")
equals(QT_MAJOR_VERSION, 5): lessThan(QT_MINOR_VERSION, 12): error("This project requires Qt 5.12 or later")

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    core/AugmentCacheManager.cpp \
    core/AugmentDatabaseManager.cpp \
    core/AugmentTelemetryManager.cpp \
    core/BackupManager.cpp \
    core/Logger.cpp \
    core/LicenseManager.cpp \
    core/HardwareFingerprint.cpp \
    core/ConfigurationManager.cpp \
    main.cpp \
    ui/MainWindow.cpp \
    ui/SettingsDialog.cpp \
    ui/ModernSplashScreen.cpp \
    ui/ActivationDialog.cpp


# 应用程序信息
VERSION = 1.0.0
QMAKE_TARGET_COMPANY = "AugmentCode"
QMAKE_TARGET_PRODUCT = "AugmentTool"
QMAKE_TARGET_DESCRIPTION = "Augment AI Plugin Data Cleaner Tool"
QMAKE_TARGET_COPYRIGHT = "Copyright (C) 2024 AugmentCode"

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

HEADERS += \
    core/AugmentCacheManager.h \
    core/AugmentDatabaseManager.h \
    core/AugmentTelemetryManager.h \
    core/BackupManager.h \
    core/Logger.h \
    core/LicenseManager.h \
    core/HardwareFingerprint.h \
    core/ConfigurationManager.h \
    pch.h \
    ui/MainWindow.h \
    ui/SettingsDialog.h \
    ui/ModernSplashScreen.h \
    ui/ActivationDialog.h

FORMS += \
    ui/mainwindow.ui

RESOURCES += \
    resources.qrc

# Windows 资源文件
win32:RC_FILE = AugmentTool_resource.rc

# Windows 应用程序清单文件（管理员权限）
win32:QMAKE_MANIFEST = AugmentTool.exe.manifest
